import uuid

from django.contrib.auth.models import User
from django.db import models

from bowenmfb.modules.choices import APPROVAL_STATUS_CHOICES, GENDER_TYPE_CHOICES
from bowenmfb.modules.utils import decrypt_text, mask_number


class BaseModel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True


class BankConstantTable(BaseModel):
    name = models.CharField(max_length=200)
    short_name = models.CharField(max_length=50, blank=True, null=True)
    support_email = models.CharField(max_length=50, blank=True, null=True)
    support_phone = models.CharField(max_length=50, blank=True, null=True)
    website = models.CharField(max_length=50, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    logo = models.ImageField(upload_to="bank-logo", blank=True, null=True)
    savings_product_code = models.IntegerField(blank=True, null=True)
    transfer_fee = models.FloatField(default=0.0)
    auth_token = models.TextField(blank=True, null=True)
    institution_code = models.TextField(blank=True, null=True)
    mfb_code = models.TextField(blank=True, null=True)
    app_version = models.IntegerField(default=1)

    # BankOne API Base URLs
    dev_base_url = models.URLField(blank=True, null=True, help_text="Development/Staging BankOne API base URL")
    prod_base_url = models.URLField(blank=True, null=True, help_text="Production BankOne API base URL")

    def __str__(self):
        return self.name


class AccountOfficer(BaseModel):
    code = models.CharField(max_length=100)
    name = models.CharField(max_length=200, null=True, blank=True)
    email = models.CharField(max_length=300, null=True, blank=True)
    gender = models.CharField(max_length=20, null=True, blank=True)
    phone_number = models.CharField(max_length=50, null=True, blank=True)

    def __str__(self):
        return f"{self.code} - {self.name}"


class Company(BaseModel):
    name = models.CharField(max_length=300)
    bank_customer_id = models.CharField(max_length=200, unique=True, null=True)  # Gotten from provider when corporate customer is created
    active = models.BooleanField(default=False)
    address = models.CharField(max_length=500)
    email = models.EmailField()
    phone_number = models.CharField(max_length=50)
    website = models.CharField(max_length=300, blank=True, null=True)
    tax_number = models.TextField(blank=True, null=True)
    registration_number = models.TextField(blank=True, null=True)
    registration_date = models.DateTimeField(blank=True, null=True)

    @property
    def get_masked_tax_number(self):
        return mask_number(self.tax_number)

    @property
    def get_registration_number(self):
        return mask_number(self.registration_number)

    def __str__(self):
        return f"{self.name} - {self.bank_customer_id}"


class Customer(BaseModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)
    bank_customer_id = models.CharField(max_length=200, null=True, blank=True)
    other_name = models.CharField(max_length=100, blank=True, null=True)
    address = models.CharField(max_length=100, blank=True, null=True)
    dob = models.CharField(max_length=50, null=True, blank=True)
    gender = models.CharField(max_length=50, choices=GENDER_TYPE_CHOICES, default="male")
    phone_number = models.CharField(max_length=15)
    state_of_origin = models.CharField(max_length=300)
    bvn_number = models.TextField()
    nin_number = models.TextField()
    is_verified = models.BooleanField(default=False)
    verification_token = models.TextField(blank=True, null=True)
    verification_token_expiry = models.DateTimeField(blank=True, null=True)
    approval_pin = models.TextField(blank=True, null=True)
    image = models.ImageField(upload_to='profile_picture', blank=True, null=True)
    active = models.BooleanField(default=False)
    account_officer = models.ForeignKey(AccountOfficer, on_delete=models.SET_NULL, blank=True, null=True)

    @property
    def get_decrypted_verification_token(self):
        return decrypt_text(self.verification_token)

    @property
    def get_decrypted_bvn(self):
        return decrypt_text(self.bvn_number)

    def get_decrypted_approval_pin(self):
        return decrypt_text(self.approval_pin)

    @property
    def get_decrypted_nin(self):
        return decrypt_text(self.nin_number)

    @property
    def get_masked_bvn(self):
        return mask_number(self.get_decrypted_bvn)

    @property
    def get_masked_nin(self):
        return mask_number(self.get_decrypted_nin)

    @property
    def get_customer_detail(self):
        data = dict()
        data["first_name"] = str(self.user.first_name).upper()
        data["last_name"] = str(self.user.last_name).upper()
        data["other_name"] = str(self.other_name).upper()
        data["email"] = self.user.email
        data["username"] = self.user.username
        data["gender"] = self.gender
        data["dob"] = self.dob
        data["phone_no"] = self.phone_number
        data["address"] = self.address
        data["bank_customer_id"] = self.bank_customer_id
        data["staff"] = self.user.is_staff
        return data

    @property
    def get_full_name(self):
        return str(self.user.get_full_name()) + " " + self.other_name

    def __str__(self):
        return f"{self.user.first_name}-{self.user.last_name}"


class CompanyCreationRequest(BaseModel):
    # account_opened_by = models.ForeignKey(Customer, on_delete=models.SET_NULL, blank=True, null=True)
    business_email = models.EmailField()
    business_name = models.CharField(max_length=200)
    business_phone_number = models.CharField(max_length=50)
    business_address = models.CharField(max_length=250)
    business_website = models.CharField(max_length=300, blank=True, null=True)
    business_sector = models.CharField(max_length=150)
    contact_person_name = models.CharField(max_length=200, null=True, blank=True)
    contact_person_phone = models.CharField(max_length=50, null=True, blank=True)
    business_tax_number = models.TextField(blank=True, null=True)
    business_registration_number = models.TextField(blank=True, null=True)
    business_registration_date = models.DateTimeField(blank=True, null=True)
    signatories = models.TextField(blank=True, null=True)
    directors = models.TextField(blank=True, null=True)
    account_creation_response = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=50, choices=APPROVAL_STATUS_CHOICES, default="pending")
    checked_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name="company_checked_by")
    verified_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name="company_verified_by")
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name="company_approved_by")
    rejected_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name="company_rejected_by")
    rejection_reason = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.business_name


class CompanyAccount(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, blank=True, null=True)
    account_number = models.CharField(max_length=20, null=True, blank=True)
    bank_one_account_number = models.CharField(max_length=100, null=True, blank=True)
    product_code = models.CharField(max_length=200, blank=True, null=True)
    active = models.BooleanField(default=True)
    account_officer = models.ForeignKey(AccountOfficer, on_delete=models.SET_NULL, blank=True, null=True)

    @property
    def get_masked_account_number(self):
        return mask_number(self.account_number)

    def __str__(self):
        if self.company:
            return f"{self.company.name} - {self.account_number}"
        else:
            return self.account_number


