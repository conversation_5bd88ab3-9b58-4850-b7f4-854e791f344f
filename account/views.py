from drf_spectacular.utils import extend_schema
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from .serializers import *
from bowenmfb.modules.exceptions import raise_serializer_error_msg


class AccountCreationAPIView(APIView):
    permission_classes = []

    @extend_schema(request=CompanyCreationRequestIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = CompanyCreationRequestIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class SignUpAPIView(APIView):
    permission_classes = []

    @extend_schema(request=SignUpSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = SignUpSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class LoginAPIView(APIView):
    permission_classes = []

    @extend_schema(request=LoginSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = LoginSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class EmailVerificationAPIView(APIView):
    permission_classes = []

    @extend_schema(request=EmailVerificationSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = EmailVerificationSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class RequestVerificationCodeAPIView(APIView):
    permission_classes = []

    @extend_schema(request=RequestVerificationLinkSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = RequestVerificationLinkSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class ChangePasswordAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=ChangePasswordSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ChangePasswordSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class ResetPasswordAPIView(APIView):
    permission_classes = []

    @extend_schema(request=ResetPasswordSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ResetPasswordSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)








