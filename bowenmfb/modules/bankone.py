import requests
import logging
import base64
from typing import Dict, Any, Optional, List
from django.conf import settings

from account.models import BankConstantTable
from .exceptions import (
    BankOneAPIError,
    BankOneAuthenticationError,
    BankOneNetworkError,
)
from .utils import log_request, encrypt_text, decrypt_text


class BankOneClient:
    """
    BankOne API Client for core banking operations.

    This client handles all BankOne API interactions including customer management,
    account operations, transfers, and transaction queries.
    """

    # Fallback Base URLs (used only if not configured in database)
    FALLBACK_BASE_URLS = {
        'dev': 'https://staging.mybankone.com',
        'prod': 'https://mybankone.com'
    }

    def __init__(self, timeout: int = 30, environment: str = None):
        """
        Initialize BankOne client.

        Args:
            timeout: Request timeout in seconds
            environment: Environment ('dev' or 'prod'). If None, auto-detects from Django settings
        """
        self.timeout = timeout
        self.logger = logging.getLogger(__name__)
        self._bank_config = None
        self._environment = environment
        self._base_url = None

    @property
    def environment(self) -> str:
        """Get current environment."""
        if self._environment:
            return self._environment

        # Auto-detect from Django settings
        try:
            debug = getattr(settings, 'DEBUG', True)
            return 'dev' if debug else 'prod'
        except Exception:
            return 'dev'  # Default to dev

    @property
    def base_url(self) -> str:
        """Get base URL for current environment."""
        if not self._base_url:
            env = self.environment

            # First try to get from database configuration
            try:
                config = self.bank_config
                if env == 'dev' and config.dev_base_url:
                    self._base_url = config.dev_base_url
                elif env == 'prod' and config.prod_base_url:
                    self._base_url = config.prod_base_url

                if self._base_url:
                    return self._base_url
            except Exception:
                pass

            # Second, try to get from Django settings
            try:
                self._base_url = getattr(settings, 'BANKONE_BASE_URL', None)
                if self._base_url:
                    return self._base_url
            except Exception:
                pass

            # Finally, fallback to hardcoded URLs
            if env not in self.FALLBACK_BASE_URLS:
                raise BankOneAPIError(f"Unsupported environment: {env}")
            self._base_url = self.FALLBACK_BASE_URLS[env]

        return self._base_url

    @property
    def bank_config(self) -> BankConstantTable:
        """Get bank configuration from database."""
        if not self._bank_config:
            try:
                self._bank_config = BankConstantTable.objects.first()
                if not self._bank_config:
                    raise BankOneAPIError("Bank configuration not found in database")
            except Exception as e:
                raise BankOneAPIError(f"Failed to load bank configuration: {str(e)}")
        return self._bank_config

    @property
    def auth_token(self) -> str:
        """Get and decrypt authentication token."""
        encrypted_token = self.bank_config.auth_token
        if not encrypted_token:
            raise BankOneAuthenticationError("Authentication token not configured")

        try:
            # Decrypt the token using the utility function
            decrypted_token = decrypt_text(encrypted_token)
            return decrypted_token
        except Exception as e:
            raise BankOneAuthenticationError(f"Failed to decrypt authentication token: {str(e)}")

    @property
    def institution_code(self) -> str:
        """Get institution code."""
        code = self.bank_config.institution_code
        if not code:
            raise BankOneAPIError("Institution code not configured")
        return code

    def _build_url(self, endpoint: str) -> str:
        """
        Build full URL from endpoint.

        Args:
            endpoint: API endpoint path

        Returns:
            Full URL
        """
        return f"{self.base_url}{endpoint}"

    def _make_request(
        self,
        method: str,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make HTTP request to BankOne API.

        Args:
            method: HTTP method (GET, POST)
            url: Full API URL
            data: Request payload for POST requests
            params: Query parameters for GET requests

        Returns:
            API response as dictionary

        Raises:
            BankOneNetworkError: For network/connection issues
            BankOneAPIError: For API errors
        """
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

        # Log request details
        log_request(f"BankOne API Request: {method} {url}")
        if data:
            log_request(f"Request payload: {data}")
        if params:
            log_request(f"Request params: {params}")

        try:
            if method.upper() == 'GET':
                response = requests.get(
                    url,
                    params=params,
                    headers=headers,
                    timeout=self.timeout
                )
            elif method.upper() == 'POST':
                response = requests.post(
                    url,
                    json=data,
                    headers=headers,
                    timeout=self.timeout
                )
            else:
                raise BankOneAPIError(f"Unsupported HTTP method: {method}")

            # Log response
            log_request(f"BankOne API Response Status: {response.status_code}")
            log_request(f"BankOne API Response: {response.text}")

            # Check for HTTP errors
            if response.status_code >= 400:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                if response.status_code == 401:
                    raise BankOneAuthenticationError(error_msg)
                else:
                    raise BankOneAPIError(error_msg)

            # Parse JSON response
            try:
                return response.json()
            except ValueError as e:
                raise BankOneAPIError(f"Invalid JSON response: {str(e)}")

        except requests.exceptions.Timeout:
            raise BankOneNetworkError("Request timeout")
        except requests.exceptions.ConnectionError:
            raise BankOneNetworkError("Connection error")
        except requests.exceptions.RequestException as e:
            raise BankOneNetworkError(f"Network error: {str(e)}")

    def _multiply_amount(self, amount: float) -> int:
        """
        Multiply amount by 100 as required by BankOne API.

        Args:
            amount: Amount in naira

        Returns:
            Amount in kobo (multiplied by 100)
        """
        return int(amount * 100)

    # Customer Management Methods

    def create_customer(
        self,
        last_name: str,
        other_names: str,
        city: str,
        address: str,
        gender: int,  # 0 for Male, 1 for Female
        date_of_birth: str,  # Format: YYYY-MM-DD
        phone_no: str,
        place_of_birth: str,
        national_identity_no: str,
        next_of_kin_name: str,
        next_of_kin_phone_number: str,
        referral_name: str,
        referral_phone_no: str,
        bvn: str,
        email: str,
        has_complete_documentation: bool = True,
        account_officer_code: str = "001",
        customer_passport_in_bytes: str = ""
    ) -> Dict[str, Any]:
        """
        Create individual customer.

        Args:
            last_name: Customer's last name
            other_names: Customer's other names
            city: Customer's city
            address: Customer's address
            gender: Gender (0=Male, 1=Female)
            date_of_birth: Date of birth (YYYY-MM-DD)
            phone_no: Phone number
            place_of_birth: Place of birth
            national_identity_no: National ID number
            next_of_kin_name: Next of kin name
            next_of_kin_phone_number: Next of kin phone
            referral_name: Referral name
            referral_phone_no: Referral phone
            bvn: Bank Verification Number
            email: Email address
            has_complete_documentation: Documentation status
            account_officer_code: Account officer code
            customer_passport_in_bytes: Base64 encoded passport photo

        Returns:
            Customer creation response
        """
        url = self._build_url(f"/BankOneWebAPI/api/Customer/CreateCustomer/2?authToken={self.auth_token}")

        payload = {
            "LastName": last_name,
            "OtherNames": other_names,
            "City": city,
            "Address": address,
            "Gender": gender,
            "DateOfBirth": date_of_birth,
            "PhoneNo": phone_no,
            "PlaceOfBirth": place_of_birth,
            "NationalIdentityNo": national_identity_no,
            "NextOfKinName": next_of_kin_name,
            "NextOfKinPhoneNumber": next_of_kin_phone_number,
            "ReferralName": referral_name,
            "ReferralPhoneNo": referral_phone_no,
            "BankVerificationNumber": bvn,
            "Email": email,
            "HasCompleteDocumentation": has_complete_documentation,
            "AccountOfficerCode": account_officer_code,
            "customerPassportInBytes": customer_passport_in_bytes
        }

        return self._make_request("POST", url, data=payload)

    def create_organization_customer(
        self,
        name: str,
        phone_no: str,
        postal_address: str,
        business_phone_no: str,
        tax_id_no: str,
        business_name: str,
        trade_name: str,
        industrial_sector: str,
        email: str,
        address: str,
        company_reg_date: str,  # ISO format: 2001-09-19T20:22:52.340Z
        contact_person_name: str = "",
        business_type: str = "",
        business_nature: str = "",
        web_address: str = "",
        date_incorporated: str = "",
        business_commencement_date: str = "",
        registration_number: str = "",
        customer_members: List[str] = None,
        the_directors: List[str] = None
    ) -> Dict[str, Any]:
        """
        Create organization/corporate customer.

        Args:
            name: Organization name
            phone_no: Phone number
            postal_address: Postal address
            business_phone_no: Business phone number
            tax_id_no: Tax ID number
            business_name: Business name
            trade_name: Trade name
            industrial_sector: Industrial sector
            email: Email address
            address: Physical address
            company_reg_date: Company registration date
            contact_person_name: Contact person name
            business_type: Type of business
            business_nature: Nature of business
            web_address: Website address
            date_incorporated: Date incorporated
            business_commencement_date: Business commencement date
            registration_number: Registration number
            customer_members: List of customer member IDs
            the_directors: List of director customer IDs

        Returns:
            Organization customer creation response
        """
        url = self._build_url(f"/BankOneWebAPI/api/Customer/CreateOrganisationCustomer/2?authToken={self.auth_token}")

        payload = {
            "Name": name,
            "PhoneNo": phone_no,
            "PostalAddress": postal_address,
            "BusinessPhoneNo": business_phone_no,
            "TaxIDNo": tax_id_no,
            "BusinessName": business_name,
            "TradeName": trade_name,
            "IndustrialSector": industrial_sector,
            "Email": email,
            "Address": address,
            "CompanyRegDate": company_reg_date,
            "ContactPersonName": contact_person_name,
            "BusinessType": business_type,
            "BusinessNature": business_nature,
            "WebAddress": web_address,
            "DateIncorporated": date_incorporated,
            "BusinessCommencementDate": business_commencement_date,
            "RegistrationNumber": registration_number,
            "CustomerMembers": customer_members or [],
            "TheDirectors": the_directors or []
        }

        return self._make_request("POST", url, data=payload)

    # Account Management Methods

    def create_account_quick(
        self,
        transaction_tracking_ref: str,
        account_opening_tracking_ref: str,
        product_code: str,
        customer_id: str,
        last_name: str,
        other_names: str,
        bvn: str,
        phone_no: str,
        gender: str,  # "0" for Male, "1" for Female
        place_of_birth: str,
        date_of_birth: str,  # Format: YYYY-MM-DD
        address: str,
        account_officer_code: str,
        email: str,
        notification_preference: int = 3,
        transaction_permission: str = "0",
        account_tier: str = "1"
    ) -> Dict[str, Any]:
        """
        Create account quickly.

        Args:
            transaction_tracking_ref: Transaction tracking reference
            account_opening_tracking_ref: Account opening tracking reference
            product_code: Product code (e.g., "101")
            customer_id: Customer ID
            last_name: Customer's last name
            other_names: Customer's other names
            bvn: Bank Verification Number
            phone_no: Phone number
            gender: Gender ("0"=Male, "1"=Female)
            place_of_birth: Place of birth
            date_of_birth: Date of birth (YYYY-MM-DD)
            address: Address
            account_officer_code: Account officer code
            email: Email address
            notification_preference: Notification preference
            transaction_permission: Transaction permission
            account_tier: Account tier

        Returns:
            Account creation response
        """
        url = self._build_url(f"/BankOneWebAPI/api/Account/CreateAccountQuick/2?authToken={self.auth_token}")

        payload = {
            "TransactionTrackingRef": transaction_tracking_ref,
            "AccountOpeningTrackingRef": account_opening_tracking_ref,
            "ProductCode": product_code,
            "CustomerID": customer_id,
            "LastName": last_name,
            "OtherNames": other_names,
            "BVN": bvn,
            "PhoneNo": phone_no,
            "Gender": gender,
            "PlaceOfBirth": place_of_birth,
            "DateOfBirth": date_of_birth,
            "Address": address,
            "AccountOfficerCode": account_officer_code,
            "Email": email,
            "NotificationPreference": notification_preference,
            "TransactionPermission": transaction_permission,
            "AccountTier": account_tier
        }

        return self._make_request("POST", url, data=payload)

    def get_account_by_number(self, account_number: str) -> Dict[str, Any]:
        """
        Get account details by account number.

        Args:
            account_number: Account number

        Returns:
            Account details including balances
        """
        url = self._build_url("/BankOneWebAPI/api/Account/GetAccountByAccountNumber/2")
        params = {
            "authToken": self.auth_token,
            "accountNumber": account_number
        }

        return self._make_request("GET", url, params=params)

    def get_accounts_by_customer_id(self, customer_id: str) -> Dict[str, Any]:
        """
        Get all accounts for a customer.

        Args:
            customer_id: Customer ID

        Returns:
            Customer details with all accounts
        """
        url = self._build_url("/BankOneWebAPI/api/Account/GetAccountsByCustomerId/2")
        params = {
            "authToken": self.auth_token,
            "customerId": customer_id
        }

        return self._make_request("GET", url, params=params)

    def generate_account_statement(
        self,
        account_number: str,
        from_date: str,  # Format: YYYY-MM-DD
        to_date: str,    # Format: YYYY-MM-DD
        is_pdf: bool = True,
        show_transaction_date: bool = True,
        show_reversed_transactions: bool = True
    ) -> Dict[str, Any]:
        """
        Generate account statement.

        Args:
            account_number: Account number
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            is_pdf: Generate PDF format
            show_transaction_date: Show transaction dates
            show_reversed_transactions: Show reversed transactions

        Returns:
            Statement response with base64 PDF data
        """
        url = self._build_url("/BankOneWebAPI/api/Account/GenerateAccountStatement2/2")
        params = {
            "authToken": self.auth_token,
            "accountNumber": account_number,
            "fromDate": from_date,
            "toDate": to_date,
            "isPdf": str(is_pdf).lower(),
            "showTransactionDate": str(show_transaction_date).lower(),
            "showReversedTransactions": str(show_reversed_transactions).lower(),
            "institutionCode": self.institution_code
        }

        return self._make_request("GET", url, params=params)

    def get_customer_by_account_number(self, account_number: str) -> Dict[str, Any]:
        """
        Get customer details by account number.

        Args:
            account_number: Account number

        Returns:
            Customer details
        """
        url = self._build_url("/BankOneWebAPI/api/Customer/GetByAccountNumber/2")
        params = {
            "authToken": self.auth_token,
            "accountNumber": account_number
        }

        return self._make_request("GET", url, params=params)

    # Account Control Methods

    def freeze_account(
        self,
        account_no: str,
        authentication_code: str,
        reference_id: str,
        reason: str
    ) -> Dict[str, Any]:
        """
        Freeze an account.

        Args:
            account_no: Account number to freeze
            authentication_code: Authentication code
            reference_id: Reference ID for the operation
            reason: Reason for freezing

        Returns:
            Freeze operation response
        """
        url = self._build_url("/thirdpartyapiservice/apiservice/Account/FreezeAccount")

        payload = {
            "AccountNo": account_no,
            "AuthenticationCode": authentication_code,
            "ReferenceID": reference_id,
            "Reason": reason
        }

        return self._make_request("POST", url, data=payload)

    def unfreeze_account(
        self,
        account_no: str,
        authentication_code: str,
        reference_id: str,
        reason: str
    ) -> Dict[str, Any]:
        """
        Unfreeze an account.

        Args:
            account_no: Account number to unfreeze
            authentication_code: Authentication code
            reference_id: Reference ID for the operation
            reason: Reason for unfreezing

        Returns:
            Unfreeze operation response
        """
        url = self._build_url("/thirdpartyapiservice/apiservice/Account/UnfreezeAccount")

        payload = {
            "AccountNo": account_no,
            "AuthenticationCode": authentication_code,
            "ReferenceID": reference_id,
            "Reason": reason
        }

        return self._make_request("POST", url, data=payload)

    # BVN and Validation Methods

    def get_bvn_details(self, bvn: str, token: str) -> Dict[str, Any]:
        """
        Get BVN details and validate BVN.

        Args:
            bvn: Bank Verification Number
            token: API token for BVN service

        Returns:
            BVN validation response with customer details
        """
        url = self._build_url("/thirdpartyapiservice/apiservice/Account/BVN/GetBVNDetails")

        payload = {
            "BVN": bvn,
            "Token": token
        }

        return self._make_request("POST", url, data=payload)

    def get_commercial_banks(self, token: str) -> Dict[str, Any]:
        """
        Get list of commercial banks.

        Args:
            token: API token

        Returns:
            List of commercial banks
        """
        url = self._build_url(f"/ThirdPartyAPIService/APIService/BillsPayment/GetCommercialBanks/{token}")

        return self._make_request("GET", url)

    def name_enquiry(
        self,
        account_number: str,
        bank_code: str,
        token: str
    ) -> Dict[str, Any]:
        """
        Perform name enquiry for account verification.

        Args:
            account_number: Account number to verify
            bank_code: Bank code
            token: API token

        Returns:
            Account name and details
        """
        url = self._build_url("/thirdpartyapiservice/apiservice/Transfer/NameEnquiry")

        payload = {
            "AccountNumber": account_number,
            "BankCode": bank_code,
            "Token": token
        }

        return self._make_request("POST", url, data=payload)

    # Transfer Methods

    def inter_bank_transfer(
        self,
        amount: float,  # Will be multiplied by 100
        payer: str,
        payer_account_number: str,
        receiver_account_number: str,
        receiver_bank_code: str,
        narration: str,
        transaction_reference: str,
        token: str,
        appzone_account: str = "",
        receiver_account_type: str = "",
        receiver_phone_number: str = "",
        receiver_name: str = "",
        receiver_bvn: str = "",
        receiver_kyc: str = "",
        nip_session_id: str = ""
    ) -> Dict[str, Any]:
        """
        Perform inter-bank transfer.

        Args:
            amount: Transfer amount in naira (will be multiplied by 100)
            payer: Payer name
            payer_account_number: Payer's account number
            receiver_account_number: Receiver's account number
            receiver_bank_code: Receiver's bank code
            narration: Transfer narration
            transaction_reference: Unique transaction reference
            token: API token
            appzone_account: Appzone account
            receiver_account_type: Receiver account type
            receiver_phone_number: Receiver phone number
            receiver_name: Receiver name
            receiver_bvn: Receiver BVN
            receiver_kyc: Receiver KYC level
            nip_session_id: NIP session ID

        Returns:
            Transfer response
        """
        url = self._build_url("/thirdpartyapiservice/apiservice/Transfer/InterBankTransfer")

        payload = {
            "Amount": str(self._multiply_amount(amount)),
            "AppzoneAccount": appzone_account,
            "Payer": payer,
            "PayerAccountNumber": payer_account_number,
            "ReceiverAccountNumber": receiver_account_number,
            "ReceiverAccountType": receiver_account_type,
            "ReceiverBankCode": receiver_bank_code,
            "ReceiverPhoneNumber": receiver_phone_number,
            "ReceiverName": receiver_name,
            "ReceiverBVN": receiver_bvn,
            "ReceiverKYC": receiver_kyc,
            "Narration": narration,
            "TransactionReference": transaction_reference,
            "NIPSessionID": nip_session_id,
            "Token": token
        }

        return self._make_request("POST", url, data=payload)

    def local_funds_transfer(
        self,
        from_account_number: str,
        amount: float,  # Will be multiplied by 100
        to_account_number: str,
        retrieval_reference: str,
        narration: str,
        authentication_key: str
    ) -> Dict[str, Any]:
        """
        Perform local/intra-bank funds transfer.

        Args:
            from_account_number: Source account number
            amount: Transfer amount in naira (will be multiplied by 100)
            to_account_number: Destination account number
            retrieval_reference: Retrieval reference
            narration: Transfer narration
            authentication_key: Authentication key

        Returns:
            Transfer response
        """
        url = self._build_url("/thirdpartyapiservice/apiservice/CoreTransactions/LocalFundsTransfer")

        payload = {
            "FromAccountNumber": from_account_number,
            "Amount": str(self._multiply_amount(amount)),
            "ToAccountNumber": to_account_number,
            "RetrievalReference": retrieval_reference,
            "Narration": narration,
            "AuthenticationKey": authentication_key
        }

        return self._make_request("POST", url, data=payload)

    # Transaction Status and Reversal Methods

    def inter_bank_transaction_status(
        self,
        amount: float,  # Will be multiplied by 100
        retrieval_reference: str,
        transaction_date: str,  # Format: YYYY-MM-DD
        token: str
    ) -> Dict[str, Any]:
        """
        Query inter-bank transaction status.

        Args:
            amount: Transaction amount in naira (will be multiplied by 100)
            retrieval_reference: Retrieval reference
            transaction_date: Transaction date (YYYY-MM-DD)
            token: API token

        Returns:
            Transaction status response
        """
        url = self._build_url("/thirdpartyapiservice/apiservice/Transactions/TransactionStatusQuery")

        payload = {
            "Amount": str(self._multiply_amount(amount)),
            "RetrievalReference": retrieval_reference,
            "TransactionDate": transaction_date,
            "TransactionType": "INTERBANKTRANSFER",
            "Token": token
        }

        return self._make_request("POST", url, data=payload)

    def local_transaction_status(
        self,
        amount: float,  # Will be multiplied by 100
        retrieval_reference: str,
        transaction_date: str,  # Format: YYYY-MM-DD
        token: str
    ) -> Dict[str, Any]:
        """
        Query local/intra-bank transaction status.

        Args:
            amount: Transaction amount in naira (will be multiplied by 100)
            retrieval_reference: Retrieval reference
            transaction_date: Transaction date (YYYY-MM-DD)
            token: API token

        Returns:
            Transaction status response
        """
        url = self._build_url("/thirdpartyapiservice/apiservice/CoreTransactions/TransactionStatusQuery")

        payload = {
            "Amount": str(self._multiply_amount(amount)),
            "RetrievalReference": retrieval_reference,
            "TransactionDate": transaction_date,
            "TransactionType": "LOCALFUNDTRANSFER",
            "Token": token
        }

        return self._make_request("POST", url, data=payload)

    def transaction_reversal(
        self,
        amount: float,  # Will be multiplied by 100
        retrieval_reference: str,
        transaction_date: str,  # Format: YYYY-MM-DD
        transaction_type: str,  # "LOCALFUNDTRANSFER" or "INTERBANKTRANSFER"
        token: str
    ) -> Dict[str, Any]:
        """
        Reverse a transaction.

        Args:
            amount: Transaction amount in naira (will be multiplied by 100)
            retrieval_reference: Retrieval reference
            transaction_date: Transaction date (YYYY-MM-DD)
            transaction_type: Transaction type ("LOCALFUNDTRANSFER" or "INTERBANKTRANSFER")
            token: API token

        Returns:
            Reversal response
        """
        url = self._build_url("/thirdpartyapiservice/apiservice/CoreTransactions/Reversal")

        payload = {
            "Amount": str(self._multiply_amount(amount)),
            "RetrievalReference": retrieval_reference,
            "TransactionDate": transaction_date,
            "TransactionType": transaction_type,
            "Token": token
        }

        return self._make_request("POST", url, data=payload)

    # Utility Methods

    def decode_pdf_statement(self, base64_pdf: str) -> bytes:
        """
        Decode base64 PDF statement to bytes.

        Args:
            base64_pdf: Base64 encoded PDF string

        Returns:
            PDF bytes
        """
        try:
            return base64.b64decode(base64_pdf)
        except Exception as e:
            raise BankOneAPIError(f"Failed to decode PDF: {str(e)}")

    def save_pdf_statement(self, base64_pdf: str, file_path: str) -> None:
        """
        Save base64 PDF statement to file.

        Args:
            base64_pdf: Base64 encoded PDF string
            file_path: Path to save the PDF file
        """
        try:
            pdf_bytes = self.decode_pdf_statement(base64_pdf)
            with open(file_path, 'wb') as f:
                f.write(pdf_bytes)
        except Exception as e:
            raise BankOneAPIError(f"Failed to save PDF: {str(e)}")


# Convenience function to get a configured BankOne client
def get_bankone_client(timeout: int = 30, environment: str = None) -> BankOneClient:
    """
    Get a configured BankOne client instance.

    Args:
        timeout: Request timeout in seconds
        environment: Environment ('dev' or 'prod'). If None, auto-detects from Django settings

    Returns:
        Configured BankOneClient instance
    """
    return BankOneClient(timeout=timeout, environment=environment)


# Utility functions for token management
def encrypt_auth_token(token: str) -> str:
    """
    Encrypt an authentication token for storage.

    Args:
        token: Plain text authentication token

    Returns:
        Encrypted token string
    """
    return encrypt_text(token)


def decrypt_auth_token(encrypted_token: str) -> str:
    """
    Decrypt an authentication token.

    Args:
        encrypted_token: Encrypted token string

    Returns:
        Plain text authentication token
    """
    return decrypt_text(encrypted_token)