from django.db import models

from account.models import BaseModel, Company, CompanyAccount, Customer
from bowenmfb.modules.choices import TRANSFER_BENEFICIARY_TYPE_CHOICES, SCHEDULE_TYPE, \
    DAYS_OF_THE_MONTH_CHOICES, DAY_OF_THE_WEEK_CHOICES, TRANSFER_SCHEDULE_STATUS, STATUS_CHOICES


class BankList(BaseModel):
    name = models.Char<PERSON>ield(max_length=300)
    code = models.Char<PERSON>ield(max_length=200)

    def __str__(self):
        return self.name


class TransferBeneficiary(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    beneficiary_type = models.Char<PERSON>ield(max_length=200, choices=TRANSFER_BENEFICIARY_TYPE_CHOICES, default='intra')
    name = models.Char<PERSON><PERSON>(max_length=200)
    bank_name = models.Char<PERSON><PERSON>(max_length=200)
    bank_code = models.Char<PERSON><PERSON>(max_length=200, blank=True, null=True)
    account_number = models.Char<PERSON>ield(max_length=200, blank=True, null=True)

    class Meta:
        verbose_name = 'Transfer Beneficiary'
        verbose_name_plural = 'Transfer Beneficiaries'

    def __str__(self):
        return f"{self.company.name}: {self.name}"


class TransferScheduler(BaseModel):
    schedule_type = models.CharField(max_length=100, choices=SCHEDULE_TYPE, default="once")
    day_of_the_month = models.CharField(max_length=200, choices=DAYS_OF_THE_MONTH_CHOICES, blank=True, null=True)
    day_of_the_week = models.CharField(max_length=100, choices=DAY_OF_THE_WEEK_CHOICES, blank=True, null=True)
    status = models.CharField(max_length=50, choices=TRANSFER_SCHEDULE_STATUS, default="inactive")
    completed = models.BooleanField(default=False)
    start_date = models.DateTimeField(null=True)
    end_date = models.DateTimeField(null=True)
    last_job_date = models.DateTimeField(null=True, blank=True)
    next_job_date = models.DateTimeField(null=True, blank=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.schedule_type}: {self.completed}"


class SingleTransferRequest(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True)
    amount = models.FloatField(default=1.0)
    description = models.CharField(max_length=60)
    from_account = models.ForeignKey(CompanyAccount, on_delete=models.SET_NULL, blank=True, null=True)
    beneficiary_account_number = models.CharField(max_length=20)
    beneficiary_name = models.CharField(max_length=100)
    beneficiary_bank_code = models.CharField(max_length=20, blank=True, null=True)
    nip_session_id = models.CharField(max_length=200, blank=True, null=True)
    bank_name = models.CharField(max_length=100, blank=True, null=True)
    beneficiary_acct_type = models.CharField(max_length=20, blank=True, null=True)
    scheduled = models.BooleanField(default=False)
    scheduler = models.ForeignKey(TransferScheduler, on_delete=models.SET_NULL, null=True, blank=True)
    decline_reason = models.CharField(max_length=250, blank=True, null=True)
    provider_response = models.TextField(blank=True, null=True)
    is_checked = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    is_approved = models.BooleanField(default=False)
    is_declined = models.BooleanField(default=False)
    checked_by = models.ForeignKey(Customer, blank=True, related_name="single_transfer_checked_by", null=True, on_delete=models.SET_NULL)
    verified_by = models.ForeignKey(Customer, blank=True, related_name="single_transfer_verified_by", null=True, on_delete=models.SET_NULL)
    approved_by = models.ForeignKey(Customer, blank=True, related_name="single_transfer_approved_by", null=True, on_delete=models.SET_NULL)
    declined_by = models.ForeignKey(Customer, blank=True, related_name="single_transfer_declined_by", null=True, on_delete=models.SET_NULL)


class SingleTransfer(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, blank=True, null=True)
    transfer_request = models.ForeignKey(SingleTransferRequest, on_delete=models.SET_NULL, blank=True, null=True)
    from_account = models.ForeignKey(CompanyAccount, on_delete=models.SET_NULL, blank=True, null=True)
    transfer_type = models.CharField(max_length=100, choices=TRANSFER_BENEFICIARY_TYPE_CHOICES, default='intra')
    beneficiary_name = models.CharField(max_length=200)
    beneficiary_bank_name = models.CharField(max_length=200)
    beneficiary_acct_number = models.CharField(max_length=20)
    status = models.CharField(max_length=100, choices=STATUS_CHOICES, default='pending')
    amount = models.FloatField(default=1.0)
    fee = models.FloatField(default=0.0)
    narration = models.TextField(blank=True, null=True)
    reference = models.CharField(max_length=12, blank=True, null=True)
    provide_reference = models.CharField(max_length=200, blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.beneficiary_name} - {self.beneficiary_bank_name}"



